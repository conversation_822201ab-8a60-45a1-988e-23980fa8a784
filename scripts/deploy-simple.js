const { ethers, network } = require("hardhat");
require("dotenv").config();

async function main() {
    console.log("🚀 Starting Simple Netronlink Token Deployment...");
    console.log("Network:", network.name);
    console.log("Chain ID:", network.config.chainId);

    const [deployer] = await ethers.getSigners();
    console.log("Deploying from account:", deployer.address);

    const balance = await deployer.provider.getBalance(deployer.address);
    console.log("Account balance:", ethers.formatEther(balance), "ETH");

    // Token distribution recipients and amounts
    const recipients = [
        process.env.PRESALE,
        process.env.DEVELOPMENT,
        process.env.MARKETING,
        process.env.STAKING,
        process.env.FINTECH,
        process.env.FUTURE,
        process.env.DAO,
    ];

    const amounts = [
        ethers.parseEther("********"), // 12%  presale
        ethers.parseEther("********"), // 15%  development
        ethers.parseEther("5000000"),  // 5%   marketing
        ethers.parseEther("********"), // 20%  staking
        ethers.parseEther("********"), // 10%  fintech
        ethers.parseEther("********"), // 14%  future
        ethers.parseEther("4000000"),  // 4%   DAO
    ];

    console.log("\n📋 Deployment Configuration:");
    console.log("Recipients:", recipients.length);
    console.log("Total allocation:", ethers.formatEther(amounts.reduce((a, b) => a + b, 0n)), "NTL");

    try {
        // Deploy Implementation Contract
        console.log("\n🔨 Deploying Netronlink Implementation...");
        const NetronlinkFactory = await ethers.getContractFactory("Netronlink");
        
        // Deploy with explicit gas settings
        const implementation = await NetronlinkFactory.deploy({
            gasLimit: 3000000, // 3M gas limit
        });
        
        console.log("⏳ Waiting for implementation deployment...");
        const implementationReceipt = await implementation.deploymentTransaction().wait();
        const implementationAddress = await implementation.getAddress();
        
        console.log("✅ Implementation deployed to:", implementationAddress);
        console.log("📄 Transaction hash:", implementationReceipt.hash);
        console.log("⛽ Gas used:", implementationReceipt.gasUsed.toString());

        // Deploy Proxy Contract
        console.log("\n🔨 Deploying ERC1967 Proxy...");
        const ProxyFactory = await ethers.getContractFactory("ERC1967Proxy");
        
        // Encode initialization data
        const initData = NetronlinkFactory.interface.encodeFunctionData("initialize", [
            recipients,
            amounts,
            process.env.TEAM_FOUNDER,
            deployer.address
        ]);

        const proxy = await ProxyFactory.deploy(implementationAddress, initData, {
            gasLimit: 2000000, // 2M gas limit
        });
        
        console.log("⏳ Waiting for proxy deployment...");
        const proxyReceipt = await proxy.deploymentTransaction().wait();
        const proxyAddress = await proxy.getAddress();
        
        console.log("✅ Proxy deployed to:", proxyAddress);
        console.log("📄 Transaction hash:", proxyReceipt.hash);
        console.log("⛽ Gas used:", proxyReceipt.gasUsed.toString());

        // Get the proxied contract instance
        const netronlink = await ethers.getContractAt("Netronlink", proxyAddress);

        // Verify deployment
        console.log("\n🔍 Verifying deployment...");
        const name = await netronlink.name();
        const symbol = await netronlink.symbol();
        const totalSupply = await netronlink.totalSupply();
        const owner = await netronlink.owner();

        console.log("\n📊 Deployment Summary:");
        console.log("========================================");
        console.log("🏗️  Network:", network.name);
        console.log("🎯 Implementation:", implementationAddress);
        console.log("🎯 Proxy (Main Contract):", proxyAddress);
        console.log("📛 Token Name:", name);
        console.log("🔤 Token Symbol:", symbol);
        console.log("💰 Total Supply:", ethers.formatEther(totalSupply), "NTL");
        console.log("👤 Contract Owner:", owner);
        console.log("========================================");

        // Verify some recipient balances
        console.log("\n💰 Token Distribution Verification:");
        for (let i = 0; i < Math.min(3, recipients.length); i++) {
            const balance = await netronlink.balanceOf(recipients[i]);
            console.log(`  Recipient ${i + 1}: ${ethers.formatEther(balance)} NTL`);
        }

        console.log("\n🎉 Deployment completed successfully!");
        console.log("💡 Use proxy address for all interactions:", proxyAddress);

        // Schedule verification after 10 seconds
        console.log("\n⏳ Scheduling contract verification in 10 seconds...");

        setTimeout(async () => {
            try {
                console.log("\n🔍 Starting automatic contract verification...");

                // Import and call the verification function directly
                const { verifyContracts } = require('./verify-deployed.js');
                await verifyContracts(implementationAddress, proxyAddress);

                console.log("✅ Automatic verification completed successfully!");

            } catch (error) {
                console.log("⚠️ Automatic verification failed:", error.message);
                console.log("You can run verification manually:");
                console.log(`npx hardhat run scripts/verify-deployed.js --network mainnet`);
                console.log(`Or set env vars: IMPLEMENTATION_ADDRESS=${implementationAddress} PROXY_ADDRESS=${proxyAddress}`);
            }
        }, 10000);

        return {
            implementation: implementationAddress,
            proxy: proxyAddress,
            token: proxyAddress // Main contract address
        };

    } catch (error) {
        console.error("\n❌ Deployment failed:", error);
        
        // If it's the transaction parsing error, try to get the transaction hash
        if (error.message.includes("invalid value for value.to")) {
            console.log("\n🔍 This appears to be a transaction parsing error.");
            console.log("The transaction may have been submitted successfully.");
            console.log("Check the latest transactions on your deployer address:");
            console.log(`https://etherscan.io/address/${deployer.address}`);
        }
        
        throw error;
    }
}

main()
    .then((result) => {
        console.log("\n✅ Deployment script completed successfully!");
        process.exit(0);
    })
    .catch((error) => {
        console.error("\n❌ Deployment failed:", error.message);
        process.exit(1);
    });
