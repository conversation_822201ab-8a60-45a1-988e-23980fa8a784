const { run, network } = require("hardhat");

async function main() {
    const implementationAddress = "0x3bcb43f4e57e478e5891aa990005325ce216354b";
    
    console.log("🔍 Verifying implementation contract...");
    console.log("Network:", network.name);
    console.log("Implementation:", implementationAddress);

    // Skip verification for local networks
    if (network.name === "hardhat" || network.name === "localhost") {
        console.log("⚠️ Skipping verification for local network:", network.name);
        return;
    }

    try {
        console.log("\n🔨 Verifying Netronlink Implementation...");
        
        await run("verify:verify", {
            address: implementationAddress,
            constructorArguments: [], // Implementation has no constructor args
            contract: "contracts/Netronlink.sol:Netronlink"
        });

        console.log("✅ Implementation contract verified!");
        console.log(`https://${network.name === 'mainnet' ? '' : network.name + '.'}etherscan.io/address/${implementationAddress}#code`);

    } catch (error) {
        if (error.message.includes("already been verified")) {
            console.log("✅ Implementation contract was already verified!");
            console.log(`https://${network.name === 'mainnet' ? '' : network.name + '.'}etherscan.io/address/${implementationAddress}#code`);
        } else {
            console.log("❌ Verification failed:", error.message);
        }
    }
}

main()
    .then(() => {
        console.log("\n🎉 Verification completed!");
        process.exit(0);
    })
    .catch((error) => {
        console.error("\n❌ Verification failed:", error);
        process.exit(1);
    });
