const { ethers, network } = require("hardhat");
require("dotenv").config();

async function main() {
    console.log("🔧 Initializing Proxy Contract...");
    console.log("Network:", network.name);
    
    const [deployer] = await ethers.getSigners();
    console.log("Deployer address:", deployer.address);
    
    // Contract addresses
    const implementationAddress = "******************************************";
    const proxyAddress = "******************************************";
    
    console.log("Implementation:", implementationAddress);
    console.log("Proxy:", proxyAddress);
    
    // Get the proxy contract
    const proxy = await ethers.getContractAt("OwnedUpgradeabilityProxy", proxyAddress);
    
    // Check current implementation
    const currentImpl = await proxy.implementation();
    console.log("Current implementation:", currentImpl);
    
    if (currentImpl === "******************************************") {
        console.log("🔄 Setting implementation address...");
        
        // Set the implementation
        const tx = await proxy.upgradeTo(implementationAddress);
        console.log("Transaction hash:", tx.hash);
        
        // Wait for confirmation
        const receipt = await tx.wait();
        console.log("✅ Implementation set! Gas used:", receipt.gasUsed.toString());
        
        // Verify the implementation was set
        const newImpl = await proxy.implementation();
        console.log("New implementation:", newImpl);
        
        if (newImpl.toLowerCase() === implementationAddress.toLowerCase()) {
            console.log("✅ Proxy successfully initialized!");
            
            // Now test the token functions through the proxy
            console.log("\n🧪 Testing token functions...");
            const netronlink = await ethers.getContractAt("Netronlink", proxyAddress);
            
            try {
                const name = await netronlink.name();
                const symbol = await netronlink.symbol();
                const totalSupply = await netronlink.totalSupply();
                const owner = await netronlink.owner();
                
                console.log("\n📊 Token Information:");
                console.log("Name:", name);
                console.log("Symbol:", symbol);
                console.log("Total Supply:", ethers.formatEther(totalSupply), "NTL");
                console.log("Owner:", owner);
                
                // Check some recipient balances
                const recipients = [
                    process.env.PRESALE,
                    process.env.DEVELOPMENT,
                    process.env.MARKETING,
                    process.env.STAKING,
                    process.env.FINTECH,
                    process.env.FUTURE,
                    process.env.DAO,
                ];
                
                console.log("\n💰 Token Distribution:");
                for (let i = 0; i < recipients.length && recipients[i]; i++) {
                    try {
                        const balance = await netronlink.balanceOf(recipients[i]);
                        console.log(`Recipient ${i + 1} (${recipients[i]}): ${ethers.formatEther(balance)} NTL`);
                    } catch (error) {
                        console.log(`Recipient ${i + 1}: Error reading balance`);
                    }
                }
                
                console.log("\n🎉 Proxy initialization completed successfully!");
                
            } catch (error) {
                console.log("⚠️ Error testing token functions:", error.message);
                console.log("The proxy may need to be initialized with token data.");
            }
            
        } else {
            console.log("❌ Failed to set implementation address");
        }
        
    } else {
        console.log("✅ Implementation already set:", currentImpl);
        
        // Test the token functions
        console.log("\n🧪 Testing token functions...");
        const netronlink = await ethers.getContractAt("Netronlink", proxyAddress);
        
        try {
            const name = await netronlink.name();
            const symbol = await netronlink.symbol();
            const totalSupply = await netronlink.totalSupply();
            const owner = await netronlink.owner();
            
            console.log("\n📊 Token Information:");
            console.log("Name:", name);
            console.log("Symbol:", symbol);
            console.log("Total Supply:", ethers.formatEther(totalSupply), "NTL");
            console.log("Owner:", owner);
            
        } catch (error) {
            console.log("⚠️ Error testing token functions:", error.message);
        }
    }
}

main()
    .then(() => {
        console.log("\n✅ Script completed!");
        process.exit(0);
    })
    .catch((error) => {
        console.error("\n❌ Script failed:", error);
        process.exit(1);
    });
