const { ethers, network } = require("hardhat");
require("dotenv").config();

async function main() {
    console.log("🔧 Initializing Proxy Contract...");
    console.log("Network:", network.name);

    const [deployer] = await ethers.getSigners();
    console.log("Deployer address:", deployer.address);

    // Contract addresses
    const implementationAddress = "******************************************";
    const proxyAddress = "******************************************";

    console.log("Implementation:", implementationAddress);
    console.log("Proxy:", proxyAddress);

    // Get the proxy contract
    const proxy = await ethers.getContractAt("OwnedUpgradeabilityProxy", proxyAddress);

    // Check current implementation
    const currentImpl = await proxy.implementation();
    console.log("Current implementation:", currentImpl);

    if (currentImpl === "******************************************") {
        console.log("🔄 Setting implementation and initializing with token distribution...");

        // Prepare initialization data
        const recipients = [
            process.env.PRESALE,
            process.env.DEVELOPMENT,
            process.env.MARKETING,
            process.env.STAKING,
            process.env.FINTECH,
            process.env.FUTURE,
            process.env.DAO,
        ];

        const amounts = [
            ethers.parseEther("12000000"), // 12%  presale
            ethers.parseEther("15000000"), // 15%  development
            ethers.parseEther("5000000"),  // 5%   marketing
            ethers.parseEther("20000000"), // 20%  staking
            ethers.parseEther("10000000"), // 10%  fintech
            ethers.parseEther("14000000"), // 14%  future
            ethers.parseEther("4000000"),  // 4%   DAO
        ];

        console.log("Recipients:", recipients.length);
        console.log("Total allocation:", ethers.formatEther(amounts.reduce((a, b) => a + b, 0n)), "NTL");
        console.log("Team & Founder:", process.env.TEAM_FOUNDER);

        // Encode initialization data
        const NetronlinkFactory = await ethers.getContractFactory("Netronlink");
        const initData = NetronlinkFactory.interface.encodeFunctionData("initialize", [
            recipients,
            amounts,
            process.env.TEAM_FOUNDER,
            deployer.address
        ]);

        // Set implementation and initialize with token distribution
        const tx = await proxy.upgradeToAndCall(implementationAddress, initData, {
            gasLimit: 2000000, // 2M gas limit
        });
        console.log("Transaction hash:", tx.hash);

        // Wait for confirmation
        const receipt = await tx.wait();
        console.log("✅ Implementation set and tokens distributed! Gas used:", receipt.gasUsed.toString());

        // Verify the implementation was set
        const newImpl = await proxy.implementation();
        console.log("New implementation:", newImpl);

        if (newImpl.toLowerCase() === implementationAddress.toLowerCase()) {
            console.log("✅ Proxy successfully initialized!");

            // Now test the token functions through the proxy
            console.log("\n🧪 Testing token functions...");
            const netronlink = await ethers.getContractAt("Netronlink", proxyAddress);

            try {
                const name = await netronlink.name();
                const symbol = await netronlink.symbol();
                const totalSupply = await netronlink.totalSupply();
                const owner = await netronlink.owner();

                console.log("\n📊 Token Information:");
                console.log("Name:", name);
                console.log("Symbol:", symbol);
                console.log("Total Supply:", ethers.formatEther(totalSupply), "NTL");
                console.log("Owner:", owner);

                // Check some recipient balances
                const recipients = [
                    process.env.PRESALE,
                    process.env.DEVELOPMENT,
                    process.env.MARKETING,
                    process.env.STAKING,
                    process.env.FINTECH,
                    process.env.FUTURE,
                    process.env.DAO,
                ];

                console.log("\n💰 Token Distribution:");
                for (let i = 0; i < recipients.length && recipients[i]; i++) {
                    try {
                        const balance = await netronlink.balanceOf(recipients[i]);
                        console.log(`Recipient ${i + 1} (${recipients[i]}): ${ethers.formatEther(balance)} NTL`);
                    } catch (error) {
                        console.log(`Recipient ${i + 1}: Error reading balance`);
                    }
                }

                console.log("\n🎉 Proxy initialization completed successfully!");

            } catch (error) {
                console.log("⚠️ Error testing token functions:", error.message);
                console.log("The proxy may need to be initialized with token data.");
            }

        } else {
            console.log("❌ Failed to set implementation address");
        }

    } else {
        console.log("✅ Implementation already set:", currentImpl);

        // Test the token functions
        console.log("\n🧪 Testing token functions...");
        const netronlink = await ethers.getContractAt("Netronlink", proxyAddress);

        try {
            const name = await netronlink.name();
            const symbol = await netronlink.symbol();
            const totalSupply = await netronlink.totalSupply();
            const owner = await netronlink.owner();

            console.log("\n📊 Token Information:");
            console.log("Name:", name);
            console.log("Symbol:", symbol);
            console.log("Total Supply:", ethers.formatEther(totalSupply), "NTL");
            console.log("Owner:", owner);

        } catch (error) {
            console.log("⚠️ Error testing token functions:", error.message);
        }
    }
}

main()
    .then(() => {
        console.log("\n✅ Script completed!");
        process.exit(0);
    })
    .catch((error) => {
        console.error("\n❌ Script failed:", error);
        process.exit(1);
    });
