const { ethers, network } = require("hardhat");
require("dotenv").config();

async function main() {
    console.log("🔧 Initializing Proxy Contract (Step by Step)...");
    console.log("Network:", network.name);
    
    const [deployer] = await ethers.getSigners();
    console.log("Deployer address:", deployer.address);
    
    // Contract addresses
    const implementationAddress = "******************************************";
    const proxyAddress = "******************************************";
    
    console.log("Implementation:", implementationAddress);
    console.log("Proxy:", proxyAddress);
    
    // Get the proxy contract
    const proxy = await ethers.getContractAt("OwnedUpgradeabilityProxy", proxyAddress);
    
    // Check current implementation
    const currentImpl = await proxy.implementation();
    console.log("Current implementation:", currentImpl);
    
    if (currentImpl === "******************************************") {
        console.log("🔄 Step 1: Setting implementation address...");
        
        // First, just set the implementation without calling initialize
        const tx1 = await proxy.upgradeTo(implementationAddress, {
            gasLimit: 500000, // Lower gas limit for simple upgrade
            maxFeePerGas: ethers.parseUnits("2", "gwei"),
            maxPriorityFeePerGas: ethers.parseUnits("1", "gwei"),
        });
        console.log("Step 1 Transaction hash:", tx1.hash);
        
        // Wait for confirmation
        const receipt1 = await tx1.wait();
        console.log("✅ Step 1 Complete! Implementation set. Gas used:", receipt1.gasUsed.toString());
        
        // Verify the implementation was set
        const newImpl = await proxy.implementation();
        console.log("New implementation:", newImpl);
        
        if (newImpl.toLowerCase() === implementationAddress.toLowerCase()) {
            console.log("✅ Implementation successfully set!");
            
            // Step 2: Now initialize the contract
            console.log("\n🔄 Step 2: Initializing contract with token distribution...");
            
            // Prepare initialization data
            const recipients = [
                process.env.PRESALE,
                process.env.DEVELOPMENT,
                process.env.MARKETING,
                process.env.STAKING,
                process.env.FINTECH,
                process.env.FUTURE,
                process.env.DAO,
            ];

            const amounts = [
                ethers.parseEther("12000000"), // 12%  presale
                ethers.parseEther("15000000"), // 15%  development
                ethers.parseEther("5000000"),  // 5%   marketing
                ethers.parseEther("20000000"), // 20%  staking
                ethers.parseEther("10000000"), // 10%  fintech
                ethers.parseEther("14000000"), // 14%  future
                ethers.parseEther("4000000"),  // 4%   DAO
            ];

            console.log("Recipients:", recipients.length);
            console.log("Total allocation:", ethers.formatEther(amounts.reduce((a, b) => a + b, 0n)), "NTL");
            console.log("Team & Founder:", process.env.TEAM_FOUNDER);

            // Get the proxied contract instance and call initialize directly
            const netronlink = await ethers.getContractAt("Netronlink", proxyAddress);
            
            const tx2 = await netronlink.initialize(
                recipients,
                amounts,
                process.env.TEAM_FOUNDER,
                deployer.address,
                {
                    gasLimit: 1500000, // 1.5M gas limit for initialization
                    maxFeePerGas: ethers.parseUnits("2", "gwei"),
                    maxPriorityFeePerGas: ethers.parseUnits("1", "gwei"),
                }
            );
            console.log("Step 2 Transaction hash:", tx2.hash);
            
            // Wait for confirmation
            const receipt2 = await tx2.wait();
            console.log("✅ Step 2 Complete! Contract initialized. Gas used:", receipt2.gasUsed.toString());
            
        } else {
            console.log("❌ Failed to set implementation address");
            return;
        }
        
    } else {
        console.log("✅ Implementation already set:", currentImpl);
    }
    
    // Test the token functions
    console.log("\n🧪 Testing token functions...");
    const netronlink = await ethers.getContractAt("Netronlink", proxyAddress);
    
    try {
        const name = await netronlink.name();
        const symbol = await netronlink.symbol();
        const totalSupply = await netronlink.totalSupply();
        const owner = await netronlink.owner();
        
        console.log("\n📊 Token Information:");
        console.log("Name:", name);
        console.log("Symbol:", symbol);
        console.log("Total Supply:", ethers.formatEther(totalSupply), "NTL");
        console.log("Owner:", owner);
        
        // Check some recipient balances
        const recipients = [
            process.env.PRESALE,
            process.env.DEVELOPMENT,
            process.env.MARKETING,
            process.env.STAKING,
            process.env.FINTECH,
            process.env.FUTURE,
            process.env.DAO,
        ];
        
        console.log("\n💰 Token Distribution:");
        for (let i = 0; i < recipients.length && recipients[i]; i++) {
            try {
                const balance = await netronlink.balanceOf(recipients[i]);
                console.log(`Recipient ${i + 1} (${recipients[i]}): ${ethers.formatEther(balance)} NTL`);
            } catch (error) {
                console.log(`Recipient ${i + 1}: Error reading balance`);
            }
        }
        
        // Check team founder balance
        try {
            const teamBalance = await netronlink.balanceOf(process.env.TEAM_FOUNDER);
            console.log(`Team & Founder (${process.env.TEAM_FOUNDER}): ${ethers.formatEther(teamBalance)} NTL`);
        } catch (error) {
            console.log("Team & Founder: Error reading balance");
        }
        
        console.log("\n🎉 Proxy initialization completed successfully!");
        
    } catch (error) {
        console.log("⚠️ Error testing token functions:", error.message);
    }
}

main()
    .then(() => {
        console.log("\n✅ Script completed!");
        process.exit(0);
    })
    .catch((error) => {
        console.error("\n❌ Script failed:", error);
        process.exit(1);
    });
